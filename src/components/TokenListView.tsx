'use client';

import { useState, useEffect, useMemo } from 'react';
import TokenCard from './TokenCard';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

interface TokenListViewProps {
  tokens: TokenInfo[];
  className?: string;
}

type TokenCategory = 'new' | 'bonding' | 'bonded';

interface CategorizedTokens {
  new: TokenInfo[];
  bonding: TokenInfo[];
  bonded: TokenInfo[];
}

export default function TokenListView({ tokens, className = '' }: TokenListViewProps) {
  const [animatedTokens, setAnimatedTokens] = useState<Set<string>>(new Set());

  // Categorize tokens based on their status
  const categorizedTokens: CategorizedTokens = useMemo(() => {
    const categories: CategorizedTokens = {
      new: [],
      bonding: [],
      bonded: []
    };

    tokens.forEach(token => {
      // Check if token has graduated (completed bonding)
      if (token.isGraduated === true) {
        categories.bonded.push(token);
      }
      // Check if token has market data (actively bonding)
      else if (
        (token.marketCap !== undefined && token.marketCap > 0) ||
        (token.priceSOL !== undefined && token.priceSOL > 0) ||
        (token.priceUSD !== undefined && token.priceUSD > 0) ||
        (token.bondingCurve !== undefined && token.bondingCurve !== '')
      ) {
        categories.bonding.push(token);
      }
      // Token is newly discovered without market data yet
      else {
        categories.new.push(token);
      }
    });

    // Sort each category by timestamp (newest first)
    Object.keys(categories).forEach(key => {
      categories[key as TokenCategory].sort((a, b) => b.timestamp - a.timestamp);
    });

    return categories;
  }, [tokens]);

  // Handle new token animations
  useEffect(() => {
    const newTokenMints = new Set(tokens.map(t => t.mint));
    const currentAnimated = new Set(animatedTokens);
    
    // Add animation class to new tokens
    tokens.forEach(token => {
      if (!currentAnimated.has(token.mint)) {
        setAnimatedTokens(prev => new Set([...prev, token.mint]));
        
        // Remove animation class after animation completes
        setTimeout(() => {
          setAnimatedTokens(prev => {
            const updated = new Set(prev);
            updated.delete(token.mint);
            return updated;
          });
        }, 1000);
      }
    });
  }, [tokens, animatedTokens]);

  const renderColumn = (title: string, tokens: TokenInfo[], category: TokenCategory) => {
    const getColumnIcon = () => {
      switch (category) {
        case 'new':
          return '🆕';
        case 'bonding':
          return '🔄';
        case 'bonded':
          return '✅';
        default:
          return '';
      }
    };

    const getColumnColor = () => {
      switch (category) {
        case 'new':
          return 'from-blue-500 to-cyan-500';
        case 'bonding':
          return 'from-yellow-500 to-orange-500';
        case 'bonded':
          return 'from-green-500 to-emerald-500';
        default:
          return 'from-gray-500 to-gray-600';
      }
    };

    return (
      <div className="flex-1 min-w-0">
        {/* Column Header */}
        <div className={`bg-gradient-to-r ${getColumnColor()} rounded-t-xl p-4 text-white`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-lg">{getColumnIcon()}</span>
              <h3 className="text-lg font-bold">{title}</h3>
            </div>
            <div className="bg-white/20 rounded-full px-3 py-1">
              <span className="text-sm font-medium">{tokens.length}</span>
            </div>
          </div>
        </div>

        {/* Column Content */}
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-b-xl border-x border-b border-gray-200/30 dark:border-gray-700/30 min-h-[400px] max-h-[600px] overflow-y-auto">
          {tokens.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <div className="text-2xl mb-2 opacity-50">{getColumnIcon()}</div>
                <p className="text-sm">
                  {category === 'new' && 'No newly discovered tokens'}
                  {category === 'bonding' && 'No tokens currently bonding'}
                  {category === 'bonded' && 'No graduated tokens yet'}
                </p>
                <p className="text-xs mt-1 opacity-75">
                  {category === 'new' && 'Tokens appear here when first detected'}
                  {category === 'bonding' && 'Tokens with market data but not graduated'}
                  {category === 'bonded' && 'Tokens that reached $69K market cap'}
                </p>
              </div>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {tokens.map((token, index) => (
                <TokenCard
                  key={token.mint}
                  token={token}
                  isAnimated={animatedTokens.has(token.mint)}
                  category={category}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`token-list-view ${className}`}>
      {/* Header Section */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            Token Status Dashboard
          </h2>
          <div className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full">
            <span className="text-sm font-medium text-purple-700 dark:text-purple-300">Real-time</span>
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Track PumpFun tokens through their lifecycle: New (just discovered) → Bonding (active trading) → Bonded (graduated at $69K)
        </p>
      </div>

      {/* Three Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {renderColumn('New', categorizedTokens.new, 'new')}
        {renderColumn('Bonding', categorizedTokens.bonding, 'bonding')}
        {renderColumn('Bonded', categorizedTokens.bonded, 'bonded')}
      </div>

      {/* Stats Footer */}
      <div className="mt-6 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
        <div className="flex items-center justify-center gap-6 text-sm text-gray-600 dark:text-gray-300">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>{categorizedTokens.new.length} New</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>{categorizedTokens.bonding.length} Bonding</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>{categorizedTokens.bonded.length} Bonded</span>
          </div>
          <div className="text-gray-400">•</div>
          <span>Total: {tokens.length} tokens</span>
        </div>
      </div>
    </div>
  );
}
